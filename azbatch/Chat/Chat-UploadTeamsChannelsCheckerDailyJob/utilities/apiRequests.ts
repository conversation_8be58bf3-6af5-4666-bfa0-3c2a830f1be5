import * as dotenv from 'dotenv';

dotenv.config();

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

const CHANNEL_SEARCH_SIZE: string = process.env['CHANNEL_SEARCH_SIZE'] ?? '50';
const CHANNEL_MAX_MONTHS: string = process.env['CHANNEL_MAX_MONTHS'] ?? '24';

export function getNighttimeRollingMonthDateRange(): { start: string; end: string } {
  const today = new Date();
  
  const maxMonths = parseInt(CHANNEL_MAX_MONTHS, 10);
  const monthsToSubtract = isNaN(maxMonths) || maxMonths <= 0 ? 1 : maxMonths;
  
  const startDate = new Date(today);
  startDate.setMonth(today.getMonth() - monthsToSubtract);
  
  const startStr = startDate.getFullYear() + '-' + 
                   String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(startDate.getDate()).padStart(2, '0');
  
  const endStr = today.getFullYear() + '-' + 
                 String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                 String(today.getDate()).padStart(2, '0');
  
  return {
    start: startStr,
    end: endStr
  };
}

export function getDaytimeRollingMonthDateRange(): { start: string; end: string } {
  const now = new Date();
  
  const maxMonths = parseInt(CHANNEL_MAX_MONTHS, 10);
  const monthsToSubtract = isNaN(maxMonths) || maxMonths <= 0 ? 1 : maxMonths;
  
  const startDate = new Date(now);
  startDate.setMonth(now.getMonth() - monthsToSubtract);

  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 1);
  
  return {
    start: startDate.toISOString(),
    end: endDate.toISOString()
  };
}

export function createChannelsMessagesRequests(teamId: string, channelId: string): TeamsChannelRequest[] {
  if (!teamId || !channelId) return [];

  return [{
    id: `${channelId}`,
    method: 'GET',
    url: `/teams/${teamId}/channels/${channelId}/messages?$top=${CHANNEL_SEARCH_SIZE}&$expand=replies`
  }];
}

export function createChannelsMembersRequests(teamId: string, channelId: string): TeamsChannelRequest[] {
  if (!teamId || !channelId) return [];

  return [{
    id: `${channelId}`,
    method: 'GET',
    url: `/teams/${teamId}/channels/${channelId}/members`
  }];
}