import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import {
  getDaytimeRollingMonthDateRange,
  getNighttimeRollingMonthDateRange,
  createChannelsMessagesRequests,
  createChannelsMembersRequests 
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeams
} from '../utilities/graph';
import { CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  IModifiedUsersChannelsData,
  ITeamsChannelsMessages,
  ITeamsChannelsMembers,
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  insertChannelsMessages,
  insertChannelsMembers
} from '../utilities/cosmos';

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processUsersChannelsDataBatch(
  logger: Logger,
  client: Client,
  modifiedUsersChannelsTableStorageData: IModifiedUsersChannelsData[],
): Promise<void> {

  logger.log(`[Impl:processUsersChannelsDataBatch] Total modifiedUsersChannelsTableStorageData to Process: ${modifiedUsersChannelsTableStorageData.length}`);
  
  const CHANNEL_MAX_USERS_CHANNELS_BATCH_COUNTS = parseInt(process.env.CHANNEL_MAX_USERS_CHANNELS_BATCH_COUNTS ?? '100');
  const splitUsersChannelsBatch = splitArrayIntoChunks(modifiedUsersChannelsTableStorageData, CHANNEL_MAX_USERS_CHANNELS_BATCH_COUNTS);
  logger.log(`[Impl:processUsersChannelsDataBatch] Total USERS_CHANNELS Batches: ${splitUsersChannelsBatch.length} | CHANNEL_MAX_USERS_CHANNELS_BATCH_COUNTS: ${CHANNEL_MAX_USERS_CHANNELS_BATCH_COUNTS}`);
  // logger.log(`[Impl:processUsersChannelsDataBatch] splitUsersChannelsBatch: ${JSON.stringify(splitUsersChannelsBatch)}`); // !!!

  const totalUsersChannelsBatch = splitUsersChannelsBatch.length;
  for (let batchIndex = 0; batchIndex < splitUsersChannelsBatch.length; batchIndex++) {
    const currentUsersChannelsBatchData = splitUsersChannelsBatch[batchIndex];
    const currentUsersChannelsBatchNum = batchIndex + 1;

    logger.log(`\n===== 1. START BATCH | USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} with ${currentUsersChannelsBatchData.length} USERS_CHANNELS INSIDE ===== `);
    // logger.log(`[Impl:processUsersChannelsDataBatch] currentUsersChannelsBatchData: ${JSON.stringify(currentUsersChannelsBatchData)}`); // !!!

    try {
      // CHANNELS_MESSAGES
      logger.log(`
        ---------------------------------
          CHANNELS MESSAGES - PROCESSING
        ---------------------------------
      `);
      await processChannelsMessagesBatch(logger, client, currentUsersChannelsBatchData, currentUsersChannelsBatchNum, totalUsersChannelsBatch);

      // CHANNELS_MEMBERS
      logger.log(`
        ---------------------------------
          CHANNELS_MEMBERS - PROCESSING 
        ---------------------------------
      `);
      await processChannelsMembersBatch(logger, client, currentUsersChannelsBatchData, currentUsersChannelsBatchNum, totalUsersChannelsBatch);
    
      splitUsersChannelsBatch[batchIndex] = [];
      if (global.gc) {
        global.gc();
      }
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processUsersChannelsDataBatch] Error Processing User Batch ${currentUsersChannelsBatchNum}: ${error}`);
      continue;
    }

    logger.log(`===== 1. END BATCH | USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} with ${currentUsersChannelsBatchData.length} USERS_CHANNELS INSIDE =====\n`);

  }
  
  splitUsersChannelsBatch.length = 0;
  logger.log(`[Impl:processUsersChannelsDataBatch] Completed Processing All User Batches.`);
}

async function processChannelsMessagesBatch(
  logger: Logger,
  client: Client,
  currentUsersChannelsBatchData: IModifiedUsersChannelsData[],
  currentUsersChannelsBatchNum: number,
  totalUsersChannelsBatch: number
): Promise<void> {

  // logger.log(`[Impl:processChannelsMessagesBatch] currentUsersChannelsBatchData: ${JSON.stringify(currentUsersChannelsBatchData)}`); // !!!
  logger.log(`[Impl:processChannelsMessagesBatch] Total currentUsersChannelsBatchData to Process: ${currentUsersChannelsBatchData.length}`);
  
  const channelMessagesBatchRequestsCreated: BatchRequestData[] = currentUsersChannelsBatchData
    .filter(data => data.channelId)
    .flatMap((data) =>
      createChannelsMessagesRequests(data?.teamId ?? '', data?.channelId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url    
      }))
    );

  logger.log(`[Impl:processChannelsMessagesBatch] Created: ${channelMessagesBatchRequestsCreated.length} CHANNELS_MESSAGES Requests`);
 
  const CHANNEL_MAX_GRAPH_API_CHANNEL_MESSAGES_BATCH_COUNTS = parseInt(process.env.CHANNEL_MAX_GRAPH_API_CHANNEL_MESSAGES_BATCH_COUNTS ?? '50');
  const splitChannelMessagesBatchRequests = splitArrayIntoChunks(channelMessagesBatchRequestsCreated, CHANNEL_MAX_GRAPH_API_CHANNEL_MESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processChannelsMessagesBatch] Total Split CHANNELS_MESSAGES Batch: ${splitChannelMessagesBatchRequests.length} | CHANNEL_MAX_GRAPH_API_CHANNEL_MESSAGES_BATCH_COUNTS: ${CHANNEL_MAX_GRAPH_API_CHANNEL_MESSAGES_BATCH_COUNTS}`);
  logger.log(`[Impl:processChannelsMessagesBatch] splitChannelMessagesBatchRequests: ${JSON.stringify(splitChannelMessagesBatchRequests)}`); // !!!

  const totalChannelsMessagesBatchRequests = splitChannelMessagesBatchRequests.length;
  for (let i = 0; i < totalChannelsMessagesBatchRequests; i++) {
    const currentChannelsMessagesBatchRequestsNum = i + 1;

    logger.log(`\n----- 2. START BATCH | API REQUEST - CHANNELS_MESSAGES: ${currentChannelsMessagesBatchRequestsNum} of ${totalChannelsMessagesBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} -----`);
    
    try {
      const currentSplitChannelsMessagesBatchRequests = splitChannelMessagesBatchRequests[i];
      // logger.log(`[Impl:processChannelsMessagesBatch] currentSplitChannelsMessagesBatchRequests == ${JSON.stringify(currentSplitChannelsMessagesBatchRequests)}`); // !!!

      const batchResultChannelsMessages = await fetchJsonBatchForTeams(logger, client, currentSplitChannelsMessagesBatchRequests);
      // // *** View Raw Response
      // logger.log(`\n\n*** [Impl:processChannelsMessagesBatch] batchResultChannelsMessages == ${JSON.stringify(batchResultChannelsMessages)}`); // !!!
      
      const totalBatchResultChannelsCount = (batchResultChannelsMessages.responses ?? []).reduce((total, response) => {
        return total + (response.body?.value?.length ?? 0);
      }, 0);
      logger.log(`\n[Impl:processChannelsMessagesBatch] Overall Total of totalBatchResultChannelsCount: ${totalBatchResultChannelsCount}`);

      // Flatten replies inline within each response
      const checkHasAttachments = (item: any): boolean => {
        return Boolean(
          item?.attachments && 
          Array.isArray(item.attachments) && 
          item.attachments.length > 0
        );
      };

      const batchResultWithReplies = {
        ...batchResultChannelsMessages,
          responses: (batchResultChannelsMessages.responses ?? []).map(response => {
          const flattenedMessages: any[] = [];
          let totalReplies = 0;
          
          (response.body?.value ?? []).forEach((message: any) => {
            const originalMessage = {
              ...message,
              hasAttachments: checkHasAttachments(message),
              replies: []
            };
            flattenedMessages.push(originalMessage);
            
            if (message.replies && Array.isArray(message.replies)) {
              totalReplies += message.replies.length;
              message.replies.forEach((reply: any) => {
                const flattenedReply = {
                  ...reply,
                  hasAttachments: checkHasAttachments(reply)
                };
                flattenedMessages.push(flattenedReply);
              });
            }
          });
          
          return {
            ...response,
            body: {
              ...response.body,
              value: flattenedMessages,
              "@odata.count": flattenedMessages.length
            }
          };
        })
      };
      // logger.log(`\n\n*** [Impl:processChannelsMessagesBatch] batchResultWithReplies == ${JSON.stringify(batchResultWithReplies)}`); // !!!
      const totalFlattenedMessages = batchResultWithReplies.responses.reduce((total, response) => {
        return total + (response.body?.value?.length ?? 0);
      }, 0);
      const totalRepliesFlattened = totalFlattenedMessages - totalBatchResultChannelsCount;
      logger.log(`[Impl:processChannelsMessagesBatch] Processed ${totalBatchResultChannelsCount} Messages + ${totalRepliesFlattened} Replies = ${totalFlattenedMessages} Total Flattened Messages`);

      if (!batchResultChannelsMessages.responses || batchResultChannelsMessages.responses.length === 0) {
        logger.log(`[Impl:processChannelsMessagesBatch] No Responses = API REQUEST - CHANNELS_MESSAGES`);
        continue;
      }

      const CHANNEL_DATETIME_SCHEDULE: string = process.env['CHANNEL_DATETIME_SCHEDULE'] ?? 'Daytime';
      let dateRange: { start: string; end: string };
      
      // const startDateTime = `${dateRange.start}T00:00:00Z`;
      // const endDateTime = `${dateRange.end}T23:59:59:99Z`; 
      const startDateTime = `2025-09-15T00:00:00Z`;
      const endDateTime = `2025-09-16T23:59:59.99Z`; 

      if (CHANNEL_DATETIME_SCHEDULE === 'Daytime') {
        dateRange = getDaytimeRollingMonthDateRange();
        logger.log(`[Impl:processChannelsMessagesBatch] ${CHANNEL_DATETIME_SCHEDULE} | Date Range: ${startDateTime} - ${endDateTime}`);

      } else if (CHANNEL_DATETIME_SCHEDULE === 'Nighttime') {
        dateRange = getNighttimeRollingMonthDateRange();
        logger.log(`[Impl:processChannelsMessagesBatch] ${CHANNEL_DATETIME_SCHEDULE} | Date Range: ${startDateTime} - ${endDateTime}`);

      } else {
        throw new Error(`Invalid CHANNEL_DATETIME_SCHEDULE: ${CHANNEL_DATETIME_SCHEDULE}. Expected 'Daytime' or 'Nighttime'.`);
      }

      const filteredBatchResultWithReplies = {
        ...batchResultWithReplies,
        responses: batchResultWithReplies.responses.map(response => {
          const filteredMessages = (response.body?.value ?? []).filter((message: any) => {
            // Date range filtering
            const messageDate = message.lastModifiedDateTime;
            const isInDateRange = messageDate >= startDateTime && messageDate <= endDateTime;
            
            // Filter out system messages
            const isValidMessageType = message.messageType !== 'systemEventMessage' && 
                                    message.messageType !== 'unknownFutureValue';

            // Must be in date range AND valid message
            return isInDateRange && isValidMessageType;
          });

          return {
            ...response,
            body: {
              ...response.body,
              value: filteredMessages,
              "@odata.count": filteredMessages.length
            }
          };
        })
      };
      // logger.log(`\n\n*** [Impl:processChannelsMessagesBatch] filteredBatchResultWithReplies == ${JSON.stringify(filteredBatchResultWithReplies)}`); // !!!
      const totalFilteredBatchResults = filteredBatchResultWithReplies.responses.reduce((total, response) => {
        return total + (response.body?.value?.length ?? 0);
      }, 0);
      logger.log(`[Impl:processChannelsMessagesBatch] Overall Total of filteredBatchResultWithReplies: ${totalFilteredBatchResults} w/o systemEventMessage and unknownFutureValue, only deleted/edited remains`);

      await processBatchResultChannelsMessages(logger, filteredBatchResultWithReplies, currentChannelsMessagesBatchRequestsNum, totalChannelsMessagesBatchRequests, currentUsersChannelsBatchNum, totalUsersChannelsBatch);

      batchResultChannelsMessages.responses = [];
      splitChannelMessagesBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processChannelsMessagesBatch] Error Processing = API REQUEST - CHANNELS_MESSAGES ${i + 1}: ${error}`);
      continue;
    }

    logger.log(`----- 2. END BATCH | API REQUEST - CHANNELS_MESSAGES: ${currentChannelsMessagesBatchRequestsNum} of ${totalChannelsMessagesBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} -----`);
  }

  channelMessagesBatchRequestsCreated.length = 0;
  splitChannelMessagesBatchRequests.length = 0;
}

async function processBatchResultChannelsMessages(
  logger: Logger,
  batchResultChannelsMessages: IBatchResponses,
  currentChannelsMessagesBatchRequestsNum: number,
  totalChannelsMessagesBatchRequests: number,
  currentUsersChannelsBatchNum: number,
  totalUsersChannelsBatch: number
): Promise<void> {

  const totalBatchResultChannelsMessages = batchResultChannelsMessages.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultChannelsMessages; j++) {
    const currentBatchResultChannelsMessagesNum = j + 1;
    const batchResultChannelsMessagesResponses = (batchResultChannelsMessages.responses ?? [])[j];
    const channelId = batchResultChannelsMessagesResponses.id;

    logger.log(`\n\n***** 3. START BATCH | PROCESSSING RESULT - CHANNELS_MESSAGES: channelId: ${channelId} / API RESULT - CHANNELS_MESSAGES: ${currentBatchResultChannelsMessagesNum} of ${totalBatchResultChannelsMessages} / API REQUEST - CHANNELS_MESSAGES: ${currentChannelsMessagesBatchRequestsNum} of ${totalChannelsMessagesBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} *****`);

    if (!batchResultChannelsMessagesResponses || batchResultChannelsMessagesResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultChannelsMessages] Skipping channelId: ${channelId} with Error Data: ${batchResultChannelsMessagesResponses?.status}`);
      continue;
    }

    const singleResultChannelsMessages = { responses: [batchResultChannelsMessagesResponses] };
    // logger.info(`[Impl:processBatchResultChannelsMessages] singleResultChannelsMessages == ${JSON.stringify(singleResultChannelsMessages)}`);  // !!!
                                                                      
    const modifiedChannelsMessagesChunk = processSingleResultChannelsMessages([singleResultChannelsMessages], logger);
    // // *** View Raw Response
    // logger.info(`\n\n[Impl:processBatchResultChannelsMessages] modifiedChannelsMessagesChunk == ${JSON.stringify(modifiedChannelsMessagesChunk)}`); // !!!

    const isEmpty = !modifiedChannelsMessagesChunk || modifiedChannelsMessagesChunk.length === 0
    if (isEmpty) {
      logger.info(`[Impl:processBatchResultChannelsMembers] modifiedChannelsMessagesChunk is EMPTY`);
    }

    // INSERTING Start...
    const totalModifiedChannelsMessageChunk = modifiedChannelsMessagesChunk.length;
    for (let k = 0; k < totalModifiedChannelsMessageChunk; k++) {
      const messagesChunk = modifiedChannelsMessagesChunk[k];
      const currentModifiedChannelsMessagesNum = k + 1;
      if (!messagesChunk) {
        logger.info(`[Impl:processBatchResultChannelsMessages] Skipping Undefined CHANNELS_MESSAGES at Index: ${k}`);
        continue;
      }
      logger.log(`+++++ 4. START BATCH | INSERTING CHANNELS_MESSAGES IN COSMOS_DB: Chunk: ${currentModifiedChannelsMessagesNum} of ${totalModifiedChannelsMessageChunk} with ${messagesChunk.body?.value?.length} CHANNELS_MESSAGES Inside +++++`);
      logger.log(`+++++ PROCESSSING RESULT - CHANNELS_MESSAGES: channelId: ${channelId} / API RESULT - CHANNELS_MESSAGES: ${currentBatchResultChannelsMessagesNum} of ${totalBatchResultChannelsMessages} / API REQUEST - CHANNELS_MESSAGES: ${currentChannelsMessagesBatchRequestsNum} of ${totalChannelsMessagesBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} +++++`);
      try {
        logger.info(`[Impl:processBatchResultChannelsMessages] INSERTING CHANNELS_MESSAGES...`);
        await insertChannelsMessages(logger, [messagesChunk]);
        logger.info(`[Impl:processBatchResultChannelsMessages] Successfully INSERTING CHANNELS_MESSAGES...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultChannelsMessages] Failed INSERTING: ${error}`);
        continue;
      }
      modifiedChannelsMessagesChunk[k] = {} as IBatchResponseData;
      logger.log(`+++++ PROCESSSING RESULT - CHANNELS_MESSAGES: channelId: ${channelId} / API RESULT - CHANNELS_MESSAGES: ${currentBatchResultChannelsMessagesNum} of ${totalBatchResultChannelsMessages} / API REQUEST - CHANNELS_MESSAGES: ${currentChannelsMessagesBatchRequestsNum} of ${totalChannelsMessagesBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} +++++`);
      logger.log(`+++++ 4. END BATCH | INSERTING CHANNELS_MESSAGES IN COSMOS_DB: Chunk: ${currentModifiedChannelsMessagesNum} of ${totalModifiedChannelsMessageChunk} with ${messagesChunk.body?.value?.length} CHANNELS_MESSAGES Inside +++++`);
    }
    // INSERTING End...

    modifiedChannelsMessagesChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));
    logger.log(`***** 3. END BATCH | PROCESSSING RESULT - CHANNELS_MESSAGES: channelId: ${channelId} / API RESULT - CHANNELS_MESSAGES: ${currentBatchResultChannelsMessagesNum} of ${totalBatchResultChannelsMessages} / API REQUEST - CHANNELS_MESSAGES: ${currentChannelsMessagesBatchRequestsNum} of ${totalChannelsMessagesBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} *****`);
  }
}

const CHANNEL_MAX_CHANNEL_MESSAGES_CHUNK_SIZE = parseInt(process.env.CHANNEL_MAX_CHANNEL_MESSAGES_CHUNK_SIZE ?? '50');
function processSingleResultChannelsMessages(
  singleResultChannelsMessages: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultChannelsMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultChannelsMessages] Skipping Invalid CHANNELS_MESSAGES Result == ${JSON.stringify(result)}`);
      continue;
    }

    for (const response of result.responses) {
      const totalChannelsMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultChannelsMessages] Total CHANNELS_MESSAGES in response.body.value: ${totalChannelsMessages}`);

      const channelId = response.id ?? '';
      const allTeamsChatMessages = response.body?.value as ITeamsChannelsMessages[];

      if (!allTeamsChatMessages || !Array.isArray(allTeamsChatMessages)) {
        logger.warn('[Impl:processSingleResultChannelsMessages] No valid messages found in response');
        return [];
      }

      // Process messages in chunks
      const chunkSize = CHANNEL_MAX_CHANNEL_MESSAGES_CHUNK_SIZE;
      for (let i = 0; i < allTeamsChatMessages.length; i += chunkSize) {
        const channelsMessagesAfterChunk = allTeamsChatMessages.slice(i, i + chunkSize);
        // logger.info(`\n\n[Impl:processSingleResultChannelsMessages] channelsMessagesAfterChunk: ${JSON.stringify(channelsMessagesAfterChunk)}`); // !!!

        processChannelsMessagesChunk(channelId, channelsMessagesAfterChunk, allProcessedData, logger);
        channelsMessagesAfterChunk.length = 0;
      }
      allTeamsChatMessages.length = 0;
    }
  }
  // logger.info(`\n\n[Impl:processSingleResultChannelsMessages] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processChannelsMessagesChunk(
  channelId: string,
  channelsMessagesAfterChunk: ITeamsChannelsMessages[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChannelsMessages[] = [];
  let skippedCount = 0;

  for (const item of channelsMessagesAfterChunk) {
    if (hasEmptyRequiredFields(item)) {
      skippedCount++;
      continue;
    }
    processedValues.push(createEncryptedMessage(item));
  }

  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} CHANNELS_MESSAGES with Null Values in this Chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: channelId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processChannelsMessagesChunk] Successfully Modified: ${processedValues.length} CHANNELS_MESSAGES Inside Chunk | CHANNEL_MAX_CHANNEL_MESSAGES_CHUNK_SIZE: ${CHANNEL_MAX_CHANNEL_MESSAGES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: ITeamsChannelsMessages): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.id) ||
         isEmpty(item.body?.content)
}

// function createEncryptedMessage(item: ITeamsChannelsMessages): ITeamsChannelsMessages {
//   return {
//     security_user_id: [],
//     id: item.id,
//     kind: "Chat",
//     replyToId: item.replyToId ? encrypt(item.replyToId) : null,
//     messageType: item.messageType ? encrypt(item.messageType) : null,
//     createdDateTime: item.createdDateTime ?? null,
//     lastModifiedDateTime: item.lastModifiedDateTime ?? null,
//     lastEditedDateTime: item.lastEditedDateTime ?? null,
//     deletedDateTime: item.deletedDateTime ?? null,
//     subject: item.subject ? encrypt(item.subject) : null,
//     chatId: item.chatId ?? null,
//     from: item.from ? {
//       application: item.from.application ? {
//         displayName: encrypt(item.from.application.displayName)
//       } : null,
//       device: item.from.device ? {
//         displayName: encrypt(item.from.device.displayName)
//       } : null,
//       user: item.from.user ? {
//         id: encrypt(item.from.user.id),
//         displayName: encrypt(item.from.user.displayName)
//       } : null,
//     } : null,
//     body: item.body ? {
//       content: encrypt(item.body.content)
//     } : null,
//     hasAttachments: item.hasAttachments,
//     channelIdentity: item.channelIdentity ? {
//       teamId: item.channelIdentity.teamId,
//       channelId: item.channelIdentity.channelId
//     } : null,
//     softDelete: false,
//     replies: []
//   };
// }

function createEncryptedMessage(item: ITeamsChannelsMessages): ITeamsChannelsMessages {
  return {
    security_user_id: [],
    id: item.id,
    kind: "Chat",
    replyToId: item.replyToId ? item.replyToId : null,
    messageType: item.messageType ? item.messageType : null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    lastEditedDateTime: item.lastEditedDateTime ?? null,
    deletedDateTime: item.deletedDateTime ?? null,
    subject: item.subject ? item.subject : null,
    chatId: item.chatId ?? null,
    from: item.from ? {
      application: item.from.application ? {
        displayName: item.from.application.displayName
      } : null,
      device: item.from.device ? {
        displayName: item.from.device.displayName
      } : null,
      user: item.from.user ? {
        id: item.from.user.id,
        displayName: item.from.user.displayName
      } : null,
    } : null,
    body: item.body ? {
      content: item.body.content
    } : null,
    hasAttachments: item.hasAttachments,
    channelIdentity: item.channelIdentity ? {
      teamId: item.channelIdentity.teamId,
      channelId: item.channelIdentity.channelId
    } : null,
    softDelete: false,
    replies: []
  };
}

/******************************************************* ========== PHASE 4 - END ========== *********************************************************/
/******************************************************* ========== PHASE 5 - START ========== *******************************************************/
export async function processChannelsMembersBatch(
  logger: Logger,
  client: Client,
  currentUsersChannelsBatchData: IModifiedUsersChannelsData[],
  currentUsersChannelsBatchNum: number,
  totalUsersChannelsBatch: number,
): Promise<void> {

  // logger.log(`[Impl:processChannelsMembersBatch] currentUsersChannelsBatchData: ${JSON.stringify(currentUsersChannelsBatchData)}`); // !!!
  logger.log(`[Impl:processChannelsMembersBatch] Total currentUsersChannelsBatchData to Process: ${currentUsersChannelsBatchData.length}`);
  
  const channelMembersBatchRequestsCreated: BatchRequestData[] = currentUsersChannelsBatchData
    .filter(data => data.channelId)
    .flatMap((data) =>
      createChannelsMembersRequests(data?.teamId ?? '', data?.channelId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processChannelsMembersBatch] Created: ${channelMembersBatchRequestsCreated.length} CHANNELS_MEMBERS Requests`);
 
  const CHANNEL_MAX_GRAPH_API_CHANNELS_MEMBERS_BATCH_COUNTS = parseInt(process.env.CHANNEL_MAX_GRAPH_API_CHANNELS_MEMBERS_BATCH_COUNTS ?? '20');
  const splitChannelsMembersBatchRequests = splitArrayIntoChunks(channelMembersBatchRequestsCreated, CHANNEL_MAX_GRAPH_API_CHANNELS_MEMBERS_BATCH_COUNTS);
  logger.log(`[Impl:processChannelsMembersBatch] Total Split CHANNELS_MEMBERS Batch: ${splitChannelsMembersBatchRequests.length} | CHANNEL_MAX_GRAPH_API_CHANNELS_MEMBERS_BATCH_COUNTS: ${CHANNEL_MAX_GRAPH_API_CHANNELS_MEMBERS_BATCH_COUNTS}`);
  // logger.log(`[Impl:processChannelsMembersBatch] splitChannelsMembersBatchRequests: ${JSON.stringify(splitChannelsMembersBatchRequests)}`); // !!!

  const totalChannelsMembersBatchRequests = splitChannelsMembersBatchRequests.length;
  for (let i = 0; i < totalChannelsMembersBatchRequests; i++) {
    const currentChannelsMembersBatchRequestsNum = i + 1;

    logger.log(`\n----- 2. START BATCH | API REQUEST - CHANNELS_MEMBERS: ${currentChannelsMembersBatchRequestsNum} of ${totalChannelsMembersBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} -----`);
    
    try {
      const currentSplitChannelsMembersBatchRequests = splitChannelsMembersBatchRequests[i];
      // logger.log(`[Impl:processChannelsMembersBatch] currentSplitChannelsMembersBatchRequests == ${JSON.stringify(currentSplitChannelsMembersBatchRequests)}`); // !!!

      const batchResultChannelsMembers = await fetchJsonBatchForTeams(logger, client, currentSplitChannelsMembersBatchRequests);
      // // *** View Raw Response
      // logger.log(`\n\n*** [Impl:processChannelsMembersBatch] batchResultChannelsMembers == ${JSON.stringify(batchResultChannelsMembers)}`); // !!!

      if (!batchResultChannelsMembers.responses || batchResultChannelsMembers.responses.length === 0) {
        logger.log(`[Impl:processChannelsMembersBatch] No Responses in API REQUEST - CHANNELS_MEMBERS`);
        continue;
      }

      // Compare the users
      const chatUsersLookup = new Map<string, Set<string>>();
      const chatUsersStatusLookup = new Map<string, Map<string, string>>();
      for (const chat of currentUsersChannelsBatchData) {
        chatUsersLookup.set(chat.channelId, new Set(chat.users.map(user => user.id)));
        const userStatusMap = new Map<string, string>();
        chat.users.forEach(user => {
          userStatusMap.set(user.id, user.status);
        });
        chatUsersStatusLookup.set(chat.channelId, userStatusMap);
      }

      const filteredBatchResultChannelsMembers = {
        responses: (batchResultChannelsMembers.responses ?? []).map(response => {
          if (!response.body?.value || !Array.isArray(response.body.value)) {
            return response;
          }
          const channelId = response.id ?? '';
          const expectedUsersForChat = chatUsersLookup.get(channelId);
          const userStatusMap = chatUsersStatusLookup.get(channelId);
          
          if (!expectedUsersForChat || !userStatusMap) {
            return {
              ...response,
              body: {
                ...response.body,
                value: []
              }
            };
          }
          
          const seenUserIds = new Set<string>();
          const validMembers = response.body.value.filter(member => {
            const userId = (member as { userId: string }).userId;
            if (!userId) return false;
            if (!expectedUsersForChat.has(userId)) return false;
            if (seenUserIds.has(userId)) return false;
            if (userStatusMap.get(userId) !== "active") return false;
            seenUserIds.add(userId);
            return true;
          });

          return {
            ...response,
            body: {
              ...response.body,
              value: validMembers
            }
          };
        })
      };
      // logger.log(`\n[Impl:processChannelsMembersBatch] filteredBatchResultChannelsMembers == ${JSON.stringify(filteredBatchResultChannelsMembers)}`);

      await processBatchResultChannelsMembers(logger, filteredBatchResultChannelsMembers, currentChannelsMembersBatchRequestsNum, totalChannelsMembersBatchRequests, currentUsersChannelsBatchNum, totalUsersChannelsBatch);

      batchResultChannelsMembers.responses = [];
      splitChannelsMembersBatchRequests[i] = [];
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error(`[Impl:processChannelsMembersBatch] Error Processing API REQUEST - CHANNELS_MEMBERS: ${i + 1}: ${error}`);
      continue;
    }
   
    logger.log(`----- 2. END BATCH | API REQUEST - CHANNELS_MEMBERS: ${currentChannelsMembersBatchRequestsNum} of ${totalChannelsMembersBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} -----`);

  }
  channelMembersBatchRequestsCreated.length = 0;
  splitChannelsMembersBatchRequests.length = 0;
}

async function processBatchResultChannelsMembers(
  logger: Logger,
  filteredBatchResultChannelsMembers: IBatchResponses,
  currentChannelsMembersBatchRequestsNum: number,
  totalChannelsMembersBatchRequests: number,
  currentUsersChannelsBatchNum: number,
  totalUsersChannelsBatch: number
): Promise<void> {

  const totalBatchResultChannelsMembers = filteredBatchResultChannelsMembers.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultChannelsMembers; j++) {
    const currentBatchResultChannelsMembersNum = j + 1;
    const batchResultChannelsMembersResponses = (filteredBatchResultChannelsMembers.responses ?? [])[j];
    const channelId = batchResultChannelsMembersResponses.id;

    logger.log(`\n\n***** 3. START BATCH | PROCESSSING RESULT - CHANNELS_MEMBERS: channelId: ${channelId} / API RESULT - CHANNELS_MEMBERS: ${currentBatchResultChannelsMembersNum} of ${totalBatchResultChannelsMembers} / API REQUEST - CHANNELS_MEMBERS: ${currentChannelsMembersBatchRequestsNum} of ${totalChannelsMembersBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} *****`);

    if (!batchResultChannelsMembersResponses || batchResultChannelsMembersResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultChannelsMembers] Skipping channelId: ${channelId} with Error Data: ${batchResultChannelsMembersResponses?.status}`);
      continue;
    }

    const singleResultChannelsMembers = { responses: [batchResultChannelsMembersResponses] };
    // logger.info(`[Impl:processBatchResultChannelsMembers] singleResultChannelsMembers == ${JSON.stringify(singleResultChannelsMembers)}`);  // !!!

    const modifiedChannelsMembersChunk = processSingleResultChannelsMembers([singleResultChannelsMembers], logger);
    // // *** View Raw Response
    // logger.info(`\n\n[Impl:processBatchResultChannelsMembers] modifiedChannelsMembersChunk == ${JSON.stringify(modifiedChannelsMembersChunk)}`);  // !!!

    const isEmpty = !modifiedChannelsMembersChunk || modifiedChannelsMembersChunk.length === 0
    if (isEmpty) {
      logger.info(`[Impl:processBatchResultChannelsMembers] modifiedChannelsMembersChunk is EMPTY`);
    }

    // UPDATING Start...
    const totalModifiedChannelsMembersChunk = modifiedChannelsMembersChunk.length;
    for (let k = 0; k < totalModifiedChannelsMembersChunk; k++) {
      const membersChunk = modifiedChannelsMembersChunk[k];
      const currentModifiedMembersChunkNum = k + 1;
      if (!membersChunk) {
        logger.info(`[Impl:processBatchResultChannelsMembers] Skipping Undefined CHANNELS_MESSAGES at Index: ${k}`);
        continue;
      }

      logger.log(`+++++ 4. START BATCH | UPDATING CHANNELS_MEMBERS IN COSMOS_DB: Chunk: ${currentModifiedMembersChunkNum} of ${totalModifiedChannelsMembersChunk} with ${membersChunk.body?.value?.length} CHANNELS_MEMBERS Inside +++++`);
      logger.log(`+++++ PROCESSSING RESULT - CHANNELS_MEMBERS: channelId: ${channelId} / API RESULT - CHANNELS_MEMBERS: ${currentBatchResultChannelsMembersNum} of ${totalBatchResultChannelsMembers} / API REQUEST - CHANNELS_MEMBERS: ${currentChannelsMembersBatchRequestsNum} of ${totalChannelsMembersBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} +++++`);
      try {
        logger.info(`[Impl:processBatchResultChannelsMembers] UPDATING CHANNELS_MEMBERS...`);
        await insertChannelsMembers(logger, [membersChunk]);
        logger.info(`[Impl:processBatchResultChannelsMembers] Successfully UPDATING CHANNELS_MEMBERS...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultChannelsMembers] Failed UPDATING: ${error}`);
        continue;
      }
      modifiedChannelsMembersChunk[k] = {} as IBatchResponseData;
      logger.log(`+++++ PROCESSSING RESULT - CHANNELS_MEMBERS: channelId: ${channelId} / API RESULT - CHANNELS_MEMBERS: ${currentBatchResultChannelsMembersNum} of ${totalBatchResultChannelsMembers} / API REQUEST - CHANNELS_MEMBERS: ${currentChannelsMembersBatchRequestsNum} of ${totalChannelsMembersBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} +++++`);
      logger.log(`+++++ 4. END BATCH | UPDATING CHANNELS_MEMBERS IN COSMOS_DB: Chunk: ${currentModifiedMembersChunkNum} of ${totalModifiedChannelsMembersChunk} with ${membersChunk.body?.value?.length} CHANNELS_MEMBERS Inside +++++`);
    }
    // UPDATING End...

    modifiedChannelsMembersChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`***** 3. END BATCH | PROCESSSING RESULT - CHANNELS_MEMBERS: channelId: ${channelId} / API RESULT - CHANNELS_MEMBERS: ${currentBatchResultChannelsMembersNum} of ${totalBatchResultChannelsMembers} / API REQUEST - CHANNELS_MEMBERS: ${currentChannelsMembersBatchRequestsNum} of ${totalChannelsMembersBatchRequests} / USERS_CHANNELS BATCH: ${currentUsersChannelsBatchNum} of ${totalUsersChannelsBatch} *****`);
  }
}

const CHANNEL_MAX_CHANNELS_MEMBERS_CHUNK_SIZE = parseInt(process.env.CHANNEL_MAX_CHANNELS_MEMBERS_CHUNK_SIZE ?? '50');
function processSingleResultChannelsMembers(
  singleResultChannelsMembers: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultChannelsMembers) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultChannelsMembers] Skipping Invalid CHANNELS_MEMBERS Result == ${JSON.stringify(result)}`);
      continue;
    }

    for (const response of result.responses) {
      const totalChannelsMembers = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultChannelsMembers] Total CHANNELS_MEMBERS in response.body.value: ${totalChannelsMembers}`);

      const teamsId = response.id ?? '';
      const allChannelsMembers = response.body?.value as ITeamsChannelsMembers[];
      // logger.log(`\n\n[Impl:processSingleResultChannelsMembers] allChannelsMembers: ${JSON.stringify(allChannelsMembers)}`); // !!!

      // Process members in chunks
      const chunkSize = CHANNEL_MAX_CHANNELS_MEMBERS_CHUNK_SIZE;
      for (let i = 0; i < allChannelsMembers.length; i += chunkSize) {
        const channelsMembersAfterChunk = allChannelsMembers.slice(i, i + chunkSize);
        // logger.info(`\n\n[Impl:processSingleResultChannelsMembers] channelsMembersAfterChunk: ${JSON.stringify(channelsMembersAfterChunk)}`); // !!!

        processChannelsMembersBatchChunk(teamsId, channelsMembersAfterChunk, allProcessedData, logger);
        channelsMembersAfterChunk.length = 0;
      }
      allChannelsMembers.length = 0;
    }
  }
  // logger.info(`\n\n[Impl:processSingleResultChannelsMembers] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processChannelsMembersBatchChunk(
  teamsId: string,
  channelsMembersAfterChunk: ITeamsChannelsMembers[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChannelsMembers[] = [];
  let skippedCount = 0;

  for (const item of channelsMembersAfterChunk) {
  if (hasEmptyRequiredFieldsMembers(item)) {
      skippedCount++;
      continue;
    }
    processedValues.push(createFieldMembers(item));
  }

  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} with null values in this chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processChannelsMembersBatchChunk] Successfully Modified: ${processedValues.length} CHANNELS_MEMBERS Inside Chunk | CHANNEL_MAX_CHANNELS_MEMBERS_CHUNK_SIZE: ${CHANNEL_MAX_CHANNELS_MEMBERS_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMembers(item: ITeamsChannelsMembers): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.id)
}

function createFieldMembers(item: ITeamsChannelsMembers): ITeamsChannelsMembers {
  return {
    id: item.id,
    displayName: item.displayName,
    visibleHistoryStartDateTime: item.visibleHistoryStartDateTime,
    userId: item.userId,
    email: item.email
  };
}
/******************************************************* ========== PHASE 5 - END ========== *********************************************************/