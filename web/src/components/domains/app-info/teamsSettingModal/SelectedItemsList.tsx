import * as React from 'react';
import { CloseIcon } from '@fluentui/react-icons-northstar';

// CSS
import './SelectedItemsList.scss';

export interface ISelectedItemsListProps {
  selectedItems: Set<ISelectedItem>;
  onRemoveItem: (item: ISelectedItem) => void;
}

/**
 * IUserChatItemとITeamsChatsItem
 * 両方の型のプロパティを含む統合型
 */
export interface ISelectedItem {
  // 共通プロパティ
  id: string; // chatId または channelId
  name: string;
  type: 'チャット' | 'チャネル';
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'channel';
  teamId?: string;
  // ITeamsChatsItemのみが持つプロパティ（オプショナル）
  countId?: number; // 選択順序
}

/**
 * SelectedItemsList
 * 選択されたチャット・チャネルアイテムを表示するコンポーネント
 */
const SelectedItemsList: React.FC<ISelectedItemsListProps> = (props) => {
  const {
    selectedItems,
    onRemoveItem,
  } = props;

  // 選択されたアイテムを配列に変換し、新しいアイテムを左側に表示するため逆順
  const selectedItemDetails = React.useMemo(
    () => Array.from(selectedItems).reverse(),
    [selectedItems],
  );

  // 選択されたアイテムがない場合は何も表示しない
  if (selectedItemDetails.length === 0) {
    return null;
  }

  return (
    <div className="selected-items-list">
      <div className="selected-items-container">
        {selectedItemDetails.map((item) => (
          <div key={item.id} className="selected-item">
            <span className="selected-item-type">{item.type}</span>
            <span className="selected-item-name">{item.name}</span>
            <button
              type="button"
              className="selected-item-remove"
              onClick={() => onRemoveItem(item)}
              aria-label={`${item.name}の選択を解除`}
              title={`${item.name}の選択を解除`}
            >
              <CloseIcon />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectedItemsList;
